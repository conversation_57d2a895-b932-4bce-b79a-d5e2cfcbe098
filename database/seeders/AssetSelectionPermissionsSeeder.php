<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class AssetSelectionPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create asset selection permissions
        $permissions = [
            ['name' => 'assets.select', 'guard_name' => 'web'],
            ['name' => 'assets.manage-selection', 'guard_name' => 'web'],
            ['name' => 'template-assets.view', 'guard_name' => 'web'],
            ['name' => 'template-assets.select', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }

        // Assign permissions to existing roles
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        $adminRole = Role::where('name', 'Admin')->first();
        $notaryStaffRole = Role::where('name', 'Notary Staff')->first();
        $viewerRole = Role::where('name', 'Viewer')->first();

        if ($superAdminRole) {
            // Super Admin gets all asset selection permissions
            $assetPermissions = Permission::whereIn('name', [
                'assets.select', 'assets.manage-selection', 
                'template-assets.view', 'template-assets.select'
            ])->get();
            foreach ($assetPermissions as $permission) {
                $superAdminRole->givePermissionTo($permission);
            }
        }

        if ($adminRole) {
            // Admin gets all asset selection permissions
            $assetPermissions = Permission::whereIn('name', [
                'assets.select', 'assets.manage-selection', 
                'template-assets.view', 'template-assets.select'
            ])->get();
            foreach ($assetPermissions as $permission) {
                $adminRole->givePermissionTo($permission);
            }
        }

        if ($notaryStaffRole) {
            // Notary Staff gets view and select permissions
            $staffPermissions = Permission::whereIn('name', [
                'assets.select', 'template-assets.view', 'template-assets.select'
            ])->get();
            foreach ($staffPermissions as $permission) {
                $notaryStaffRole->givePermissionTo($permission);
            }
        }

        if ($viewerRole) {
            // Viewer gets only view permission
            $viewPermission = Permission::where('name', 'template-assets.view')->first();
            if ($viewPermission) {
                $viewerRole->givePermissionTo($viewPermission);
            }
        }

        $this->command->info('Asset selection permissions created and assigned successfully!');
    }
}
