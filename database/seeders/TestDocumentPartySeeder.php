<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DocumentParty;

class TestDocumentPartySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create a test document to satisfy foreign key constraint
        $testDocument = \App\Models\Document::create([
            'document_number' => 'TEST-' . now()->format('YmdHis'),
            'user_id' => 1, // Assuming user with ID 1 exists
            'title' => 'Test Document for Party Search',
            'status' => 'draft',
        ]);

        // Create test parties for debugging
        $testParties = [
            [
                'document_id' => $testDocument->id,
                'full_name' => '<PERSON>uyễn <PERSON>n <PERSON>',
                'id_number' => '123456789012',
                'id_type' => 'cccd',
                'birth_year' => 1990,
                'current_address' => '123 Đường ABC, Quận 1, TP.HCM',
                'phone' => '0901234567',
                'email' => 'nguyen<PERSON><EMAIL>',
                'party_type' => 'party_a',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'document_id' => $testDocument->id,
                'full_name' => 'Trần Thị Bình',
                'id_number' => '987654321098',
                'id_type' => 'cccd',
                'birth_year' => 1985,
                'current_address' => '456 Đường XYZ, Quận 2, TP.HCM',
                'phone' => '0907654321',
                'email' => '<EMAIL>',
                'party_type' => 'party_b',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'document_id' => $testDocument->id,
                'full_name' => 'Lê Văn Cường',
                'id_number' => '111222333444',
                'id_type' => 'cccd',
                'birth_year' => 1988,
                'current_address' => '789 Đường DEF, Quận 3, TP.HCM',
                'phone' => '0909876543',
                'email' => '<EMAIL>',
                'party_type' => 'witness',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'document_id' => $testDocument->id,
                'full_name' => 'Phạm Thị Dung',
                'id_number' => '555666777888',
                'id_type' => 'cccd',
                'birth_year' => 1992,
                'current_address' => '321 Đường GHI, Quận 4, TP.HCM',
                'phone' => '0912345678',
                'email' => '<EMAIL>',
                'party_type' => 'party_a',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'document_id' => $testDocument->id,
                'full_name' => 'Hoàng Văn Em',
                'id_number' => '999888777666',
                'id_type' => 'cccd',
                'birth_year' => 1987,
                'current_address' => '654 Đường JKL, Quận 5, TP.HCM',
                'phone' => '0918765432',
                'email' => '<EMAIL>',
                'party_type' => 'other',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($testParties as $party) {
            DocumentParty::updateOrCreate(
                ['id_number' => $party['id_number']],
                $party
            );
        }

        $this->command->info('Created ' . count($testParties) . ' test document parties');
    }
}
