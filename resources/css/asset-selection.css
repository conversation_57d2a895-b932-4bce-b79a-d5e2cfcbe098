/**
 * Asset Selection Styles for Document Wizard
 */

/* Asset Selection Container */
.asset-selection-container {
  margin-top: 1rem;
}

.asset-group {
  margin-bottom: 2rem;
}

.asset-group-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--bs-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--bs-primary-bg-subtle);
}

/* Asset Field Cards */
.asset-field-card {
  border: 2px solid var(--bs-border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  background: var(--bs-body-bg);
}

.asset-field-card:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transform: translateY(-1px);
}

.asset-field-card.selected {
  border-color: var(--bs-primary);
  background: var(--bs-primary-bg-subtle);
  box-shadow: 0 0.25rem 0.5rem rgba(var(--bs-primary-rgb), 0.15);
}

.asset-field-card.required {
  border-left: 4px solid var(--bs-warning);
}

.asset-field-card.required.selected {
  border-left: 4px solid var(--bs-warning);
  border-color: var(--bs-primary);
}

/* Asset Field Header */
.asset-field-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.asset-field-title {
  font-weight: 600;
  color: var(--bs-heading-color);
  margin-bottom: 0.25rem;
}

.asset-field-name {
  font-size: 0.875rem;
  color: var(--bs-secondary);
  font-family: var(--bs-font-monospace);
}

.asset-field-badges {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

/* Field Type Badges */
.field-type-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.field-type-text { background: var(--bs-info-bg-subtle); color: var(--bs-info-text-emphasis); }
.field-type-number { background: var(--bs-success-bg-subtle); color: var(--bs-success-text-emphasis); }
.field-type-date { background: var(--bs-warning-bg-subtle); color: var(--bs-warning-text-emphasis); }
.field-type-select { background: var(--bs-primary-bg-subtle); color: var(--bs-primary-text-emphasis); }
.field-type-textarea { background: var(--bs-secondary-bg-subtle); color: var(--bs-secondary-text-emphasis); }
.field-type-file { background: var(--bs-danger-bg-subtle); color: var(--bs-danger-text-emphasis); }
.field-type-checkbox { background: var(--bs-dark-bg-subtle); color: var(--bs-dark-text-emphasis); }
.field-type-radio { background: var(--bs-light-bg-subtle); color: var(--bs-light-text-emphasis); }

/* Asset Field Content */
.asset-field-content {
  margin-bottom: 0.75rem;
}

.asset-field-description {
  color: var(--bs-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.asset-field-help {
  font-size: 0.8rem;
  color: var(--bs-secondary);
  font-style: italic;
  margin-top: 0.5rem;
}

/* Checkbox/Radio Controls */
.asset-field-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.asset-field-checkbox,
.asset-field-radio {
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
}

.asset-field-control-label {
  font-weight: 500;
  cursor: pointer;
  margin: 0;
}

/* Loading States */
.asset-loading {
  text-align: center;
  padding: 2rem;
  color: var(--bs-secondary);
}

.asset-loading-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid var(--bs-border-color);
  border-top-color: var(--bs-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Empty States */
.asset-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--bs-secondary);
}

.asset-empty-icon {
  font-size: 3rem;
  color: var(--bs-border-color);
  margin-bottom: 1rem;
}

.asset-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.asset-empty-description {
  font-size: 0.875rem;
}

/* Error States */
.asset-error {
  text-align: center;
  padding: 2rem;
  color: var(--bs-danger);
  background: var(--bs-danger-bg-subtle);
  border: 1px solid var(--bs-danger-border-subtle);
  border-radius: 0.5rem;
}

/* Selection Summary */
.asset-selection-summary {
  background: var(--bs-light-bg-subtle);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1.5rem;
}

.asset-selection-summary-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--bs-heading-color);
}

.asset-selection-count {
  font-size: 0.875rem;
  color: var(--bs-secondary);
}

.asset-selected-list {
  list-style: none;
  padding: 0;
  margin: 0.75rem 0 0 0;
}

.asset-selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--bs-border-color);
}

.asset-selected-item:last-child {
  border-bottom: none;
}

.asset-selected-name {
  font-weight: 500;
}

.asset-selected-type {
  font-size: 0.75rem;
  color: var(--bs-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .asset-field-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .asset-field-badges {
    align-self: flex-start;
  }
  
  .asset-field-card {
    padding: 0.75rem;
  }
}

/* Validation States */
.asset-selection-invalid {
  border-color: var(--bs-danger) !important;
  background: var(--bs-danger-bg-subtle);
}

.asset-selection-valid {
  border-color: var(--bs-success) !important;
  background: var(--bs-success-bg-subtle);
}

/* Animation for selection */
.asset-field-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.asset-field-card.selecting {
  transform: scale(0.98);
}

.asset-field-card.selected {
  animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}
