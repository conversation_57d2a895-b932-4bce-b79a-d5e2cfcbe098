/**
 * Asset Selection Module for Document Wizard
 */

'use strict';

// Global variables for asset selection
let selectedAssets = [];
let availableAssets = [];
let currentTemplate = null;

/**
 * Initialize asset selection functionality
 */
function initializeAssetSelection() {
  console.log('Initializing asset selection...');
  
  // Bind event handlers
  bindAssetSelectionEvents();
  
  // Initialize validation
  initializeAssetValidation();
}

/**
 * Bind event handlers for asset selection
 */
function bindAssetSelectionEvents() {
  // Asset card click handler
  $(document).on('click', '.asset-field-card', function(e) {
    e.preventDefault();
    const assetId = $(this).data('asset-id');
    toggleAssetSelection(assetId, this);
  });
  
  // Checkbox/radio change handler
  $(document).on('change', '.asset-field-checkbox, .asset-field-radio', function(e) {
    const assetId = $(this).data('asset-id');
    const card = $(this).closest('.asset-field-card');
    toggleAssetSelection(assetId, card[0]);
  });
  
  // Select all in group handler
  $(document).on('click', '.select-all-group', function(e) {
    e.preventDefault();
    const groupName = $(this).data('group');
    selectAllInGroup(groupName);
  });
  
  // Clear all selections handler
  $(document).on('click', '.clear-all-assets', function(e) {
    e.preventDefault();
    clearAllSelections();
  });
}

/**
 * Load assets from selected template
 */
function loadAssetsFromTemplate(templateId) {
  console.log('Loading assets from template:', templateId);
  
  if (!templateId) {
    console.warn('No template ID provided');
    return;
  }
  
  const $container = $('#assets-container');
  
  // Show loading state
  $container.html(`
    <div class="asset-loading">
      <div class="asset-loading-spinner"></div>
      <div>Đang tải danh sách tài sản từ template...</div>
    </div>
  `);
  
  // Make AJAX request
  $.ajax({
    url: '/documents/ajax/template-assets',
    type: 'GET',
    data: { template_id: templateId },
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      console.log('Assets loaded successfully:', response);
      
      if (response.success && response.asset_fields) {
        availableAssets = response.asset_fields;
        currentTemplate = response.template;
        renderAssetSelection(response.asset_fields, response.grouped_fields);
      } else {
        showAssetError('Không thể tải danh sách tài sản từ template');
      }
    },
    error: function(xhr, status, error) {
      console.error('Error loading assets:', {xhr, status, error});
      showAssetError('Có lỗi xảy ra khi tải danh sách tài sản: ' + error);
    }
  });
}

/**
 * Render asset selection interface
 */
function renderAssetSelection(assetFields, groupedFields) {
  const $container = $('#assets-container');
  
  if (!assetFields || assetFields.length === 0) {
    $container.html(`
      <div class="asset-empty">
        <div class="asset-empty-icon">
          <i class="ri-file-list-line"></i>
        </div>
        <div class="asset-empty-title">Không có tài sản nào</div>
        <div class="asset-empty-description">Template này chưa có fields tài sản nào được định nghĩa</div>
      </div>
    `);
    return;
  }
  
  let html = '<div class="asset-selection-container">';
  
  // Group assets by group_name
  const groups = {};
  assetFields.forEach(field => {
    const groupName = field.group_name || 'Thông tin cơ bản';
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(field);
  });
  
  // Render each group
  Object.keys(groups).forEach(groupName => {
    html += `
      <div class="asset-group">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="asset-group-title">${groupName}</h5>
          <button type="button" class="btn btn-sm btn-outline-primary select-all-group" data-group="${groupName}">
            <i class="ri-check-double-line me-1"></i>Chọn tất cả
          </button>
        </div>
        <div class="row g-3">
    `;
    
    groups[groupName].forEach(field => {
      html += createAssetFieldCard(field);
    });
    
    html += `
        </div>
      </div>
    `;
  });
  
  html += '</div>';
  
  // Add selection summary
  html += createSelectionSummary();
  
  $container.html(html);
  
  // Initialize tooltips if available
  if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
}

/**
 * Create asset field card HTML
 */
function createAssetFieldCard(field) {
  const isRequired = field.is_required;
  const requiredClass = isRequired ? 'required' : '';
  const requiredBadge = isRequired ? '<span class="badge bg-warning text-dark">Bắt buộc</span>' : '';
  
  return `
    <div class="col-md-6 col-lg-4">
      <div class="asset-field-card ${requiredClass}" data-asset-id="${field.id}">
        <div class="asset-field-header">
          <div class="flex-grow-1">
            <div class="asset-field-title">${field.label}</div>
            <div class="asset-field-name">${field.name}</div>
          </div>
          <div class="asset-field-badges">
            <span class="field-type-badge field-type-${field.type}">${field.type}</span>
            ${requiredBadge}
          </div>
        </div>
        
        <div class="asset-field-content">
          ${field.help_text ? `<div class="asset-field-description">${field.help_text}</div>` : ''}
          ${field.placeholder ? `<div class="asset-field-help">Placeholder: ${field.placeholder}</div>` : ''}
        </div>
        
        <div class="asset-field-control">
          <input type="checkbox" 
                 class="form-check-input asset-field-checkbox" 
                 id="asset_${field.id}" 
                 data-asset-id="${field.id}"
                 ${isRequired ? 'data-required="true"' : ''}>
          <label class="asset-field-control-label" for="asset_${field.id}">
            Chọn tài sản này
          </label>
        </div>
      </div>
    </div>
  `;
}

/**
 * Create selection summary HTML
 */
function createSelectionSummary() {
  return `
    <div class="asset-selection-summary" id="asset-selection-summary" style="display: none;">
      <div class="asset-selection-summary-title">
        <i class="ri-check-line me-2"></i>Tài sản đã chọn
      </div>
      <div class="asset-selection-count" id="asset-selection-count">
        Đã chọn 0 tài sản
      </div>
      <ul class="asset-selected-list" id="asset-selected-list">
        <!-- Selected assets will be listed here -->
      </ul>
      <div class="mt-3">
        <button type="button" class="btn btn-sm btn-outline-danger clear-all-assets">
          <i class="ri-close-line me-1"></i>Bỏ chọn tất cả
        </button>
      </div>
    </div>
  `;
}

/**
 * Toggle asset selection
 */
function toggleAssetSelection(assetId, cardElement) {
  const $card = $(cardElement);
  const $checkbox = $card.find('.asset-field-checkbox');
  const asset = availableAssets.find(a => a.id == assetId);
  
  if (!asset) {
    console.error('Asset not found:', assetId);
    return;
  }
  
  // Add selecting animation
  $card.addClass('selecting');
  setTimeout(() => $card.removeClass('selecting'), 150);
  
  const isCurrentlySelected = selectedAssets.some(a => a.id == assetId);
  
  if (isCurrentlySelected) {
    // Remove from selection
    selectedAssets = selectedAssets.filter(a => a.id != assetId);
    $card.removeClass('selected');
    $checkbox.prop('checked', false);
  } else {
    // Add to selection
    selectedAssets.push(asset);
    $card.addClass('selected');
    $checkbox.prop('checked', true);
  }
  
  // Update summary
  updateSelectionSummary();
  
  // Validate selection
  validateAssetSelection();
  
  console.log('Selected assets:', selectedAssets);
}

/**
 * Select all assets in a group
 */
function selectAllInGroup(groupName) {
  const groupAssets = availableAssets.filter(asset => asset.group_name === groupName);
  
  groupAssets.forEach(asset => {
    if (!selectedAssets.some(a => a.id === asset.id)) {
      selectedAssets.push(asset);
      const $card = $(`.asset-field-card[data-asset-id="${asset.id}"]`);
      const $checkbox = $card.find('.asset-field-checkbox');
      $card.addClass('selected');
      $checkbox.prop('checked', true);
    }
  });
  
  updateSelectionSummary();
  validateAssetSelection();
}

/**
 * Clear all selections
 */
function clearAllSelections() {
  selectedAssets = [];
  $('.asset-field-card').removeClass('selected');
  $('.asset-field-checkbox').prop('checked', false);
  updateSelectionSummary();
  validateAssetSelection();
}

/**
 * Update selection summary
 */
function updateSelectionSummary() {
  const $summary = $('#asset-selection-summary');
  const $count = $('#asset-selection-count');
  const $list = $('#asset-selected-list');
  
  if (selectedAssets.length === 0) {
    $summary.hide();
    return;
  }
  
  $summary.show();
  $count.text(`Đã chọn ${selectedAssets.length} tài sản`);
  
  let listHtml = '';
  selectedAssets.forEach(asset => {
    listHtml += `
      <li class="asset-selected-item">
        <div>
          <div class="asset-selected-name">${asset.label}</div>
          <div class="asset-selected-type">${asset.type}</div>
        </div>
        <span class="badge bg-primary">${asset.group_name}</span>
      </li>
    `;
  });
  
  $list.html(listHtml);
}

/**
 * Show asset loading error
 */
function showAssetError(message) {
  const $container = $('#assets-container');
  $container.html(`
    <div class="asset-error">
      <i class="ri-error-warning-line me-2"></i>
      ${message}
    </div>
  `);
}

/**
 * Initialize asset validation
 */
function initializeAssetValidation() {
  // This will be called from the main wizard validation
  console.log('Asset validation initialized');
}

/**
 * Validate asset selection
 */
function validateAssetSelection() {
  const hasSelection = selectedAssets.length > 0;
  const requiredAssets = availableAssets.filter(asset => asset.is_required);
  const hasAllRequired = requiredAssets.every(required => 
    selectedAssets.some(selected => selected.id === required.id)
  );
  
  const isValid = hasSelection && hasAllRequired;
  
  // Update validation state
  const $container = $('#assets-container');
  $container.removeClass('asset-selection-invalid asset-selection-valid');
  
  if (selectedAssets.length > 0) {
    $container.addClass(isValid ? 'asset-selection-valid' : 'asset-selection-invalid');
  }
  
  // Store validation result for wizard
  window.assetSelectionValid = isValid;
  
  return isValid;
}

/**
 * Get selected assets data for form submission
 */
function getSelectedAssetsData() {
  return selectedAssets.map(asset => ({
    field_id: asset.id,
    field_name: asset.name,
    field_label: asset.label,
    field_type: asset.type,
    group_name: asset.group_name,
    is_required: asset.is_required
  }));
}

// Export functions for global access
window.initializeAssetSelection = initializeAssetSelection;
window.loadAssetsFromTemplate = loadAssetsFromTemplate;
window.validateAssetSelection = validateAssetSelection;
window.getSelectedAssetsData = getSelectedAssetsData;
window.clearAllSelections = clearAllSelections;
