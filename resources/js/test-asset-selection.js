/**
 * Test Asset Selection Functionality
 * This file can be used to test the asset selection features
 */

'use strict';

// Test data
const testTemplateData = {
  success: true,
  template: {
    id: 1,
    name: "Template mua bán nhà đất cơ bản",
    description: "Template c<PERSON> bản cho hợp đồng mua bán nhà đất"
  },
  asset_fields: [
    {
      id: 1,
      name: "dia_chi",
      label: "Địa chỉ",
      type: "textarea",
      is_required: true,
      group_name: "Thông tin cơ bản",
      sort_order: 1,
      help_text: "Nhập địa chỉ đầy đủ của tài sản",
      placeholder: "Ví dụ: 123 Đường ABC, Phường XYZ, Quận 1, TP.HCM"
    },
    {
      id: 2,
      name: "dien_tich",
      label: "Diện tích (m²)",
      type: "number",
      is_required: true,
      group_name: "Thông tin cơ bản",
      sort_order: 2,
      help_text: "<PERSON>ện tích tài sản tính bằng mét vuông",
      placeholder: "Ví dụ: 100"
    },
    {
      id: 3,
      name: "gia_tri",
      label: "<PERSON><PERSON><PERSON>r<PERSON> (VND)",
      type: "number",
      is_required: true,
      group_name: "Thông tin cơ bản",
      sort_order: 3,
      help_text: "Giá trị ước tính của tài sản",
      placeholder: "Ví dụ: 5000000000"
    },
    {
      id: 4,
      name: "so_to_ban_do",
      label: "Số tờ bản đồ",
      type: "text",
      is_required: false,
      group_name: "Thông tin bổ sung",
      sort_order: 4,
      help_text: "Số tờ bản đồ theo giấy chứng nhận",
      placeholder: "Ví dụ: 15"
    },
    {
      id: 5,
      name: "so_thua",
      label: "Số thửa",
      type: "text",
      is_required: false,
      group_name: "Thông tin bổ sung",
      sort_order: 5,
      help_text: "Số thửa đất theo giấy chứng nhận",
      placeholder: "Ví dụ: 123"
    },
    {
      id: 6,
      name: "hinh_anh",
      label: "Hình ảnh tài sản",
      type: "file",
      is_required: false,
      group_name: "Tài liệu đính kèm",
      sort_order: 6,
      help_text: "Upload hình ảnh của tài sản",
      placeholder: "Chọn file hình ảnh"
    }
  ]
};

/**
 * Test asset selection rendering
 */
function testAssetSelectionRendering() {
  console.log('Testing asset selection rendering...');
  
  // Simulate loading assets from template
  if (typeof window.renderAssetSelection === 'function') {
    window.renderAssetSelection(testTemplateData.asset_fields, testTemplateData.grouped_fields);
    console.log('✓ Asset selection rendered successfully');
  } else {
    console.error('✗ renderAssetSelection function not found');
  }
}

/**
 * Test asset selection functionality
 */
function testAssetSelection() {
  console.log('Testing asset selection functionality...');
  
  // Test selecting an asset
  if (typeof window.toggleAssetSelection === 'function') {
    const testCard = document.querySelector('.asset-field-card[data-asset-id="1"]');
    if (testCard) {
      window.toggleAssetSelection(1, testCard);
      console.log('✓ Asset selection toggle works');
    } else {
      console.warn('⚠ No asset cards found for testing');
    }
  } else {
    console.error('✗ toggleAssetSelection function not found');
  }
}

/**
 * Test validation
 */
function testAssetValidation() {
  console.log('Testing asset validation...');
  
  if (typeof window.validateAssetSelection === 'function') {
    const isValid = window.validateAssetSelection();
    console.log(`✓ Validation result: ${isValid ? 'Valid' : 'Invalid'}`);
  } else {
    console.error('✗ validateAssetSelection function not found');
  }
}

/**
 * Test getting selected assets data
 */
function testGetSelectedAssetsData() {
  console.log('Testing get selected assets data...');
  
  if (typeof window.getSelectedAssetsData === 'function') {
    const selectedData = window.getSelectedAssetsData();
    console.log('✓ Selected assets data:', selectedData);
  } else {
    console.error('✗ getSelectedAssetsData function not found');
  }
}

/**
 * Run all tests
 */
function runAssetSelectionTests() {
  console.log('=== Asset Selection Tests ===');
  
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(runTests, 1000);
    });
  } else {
    setTimeout(runTests, 1000);
  }
  
  function runTests() {
    try {
      testAssetSelectionRendering();
      setTimeout(() => {
        testAssetSelection();
        testAssetValidation();
        testGetSelectedAssetsData();
        console.log('=== Tests Completed ===');
      }, 500);
    } catch (error) {
      console.error('Test error:', error);
    }
  }
}

/**
 * Mock AJAX response for testing
 */
function mockTemplateAssetsResponse() {
  // Override jQuery AJAX for testing
  if (typeof $ !== 'undefined' && $.ajax) {
    const originalAjax = $.ajax;
    $.ajax = function(options) {
      if (options.url && options.url.includes('/documents/ajax/template-assets')) {
        console.log('Mocking template assets response...');
        setTimeout(() => {
          if (options.success) {
            options.success(testTemplateData);
          }
        }, 500);
        return;
      }
      return originalAjax.apply(this, arguments);
    };
  }
}

/**
 * Initialize test environment
 */
function initializeTestEnvironment() {
  console.log('Initializing test environment...');
  
  // Mock AJAX responses
  mockTemplateAssetsResponse();
  
  // Add test template info
  $('#current-template-name').text(testTemplateData.template.name);
  $('#template-info-display').removeClass('d-none');
  
  // Set test template ID
  $('#asset_template_id').val(testTemplateData.template.id);
  
  console.log('Test environment ready');
}

// Auto-run tests if this file is loaded
if (typeof window !== 'undefined') {
  window.testAssetSelection = {
    runTests: runAssetSelectionTests,
    initTestEnvironment: initializeTestEnvironment,
    testData: testTemplateData
  };
  
  // Auto-initialize if in test mode
  if (window.location.search.includes('test=asset-selection')) {
    initializeTestEnvironment();
    runAssetSelectionTests();
  }
}
